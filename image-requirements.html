<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Requirements - Always Bank on Her</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #D4AF37;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #6B46C1;
            border-bottom: 2px solid #D4AF37;
            padding-bottom: 10px;
        }
        .image-item {
            background: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #D4AF37;
            border-radius: 5px;
        }
        .filename {
            font-weight: bold;
            color: #6B46C1;
        }
        .specs {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .note {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image Requirements for Always Bank on Her Website</h1>
        
        <div class="note">
            <strong>Note:</strong> All images should be high-quality, professional, and align with the sophisticated brand aesthetic. The website uses gradient borders and glassmorphism effects, so images should complement these modern design elements.
        </div>

        <h2>Required Images</h2>

        <div class="image-item">
            <div class="filename">images/header.png</div>
            <div>Hero section background image</div>
            <div class="specs">Recommended: 1920x1080px, PNG format, professional/corporate theme</div>
        </div>

        <div class="image-item">
            <div class="filename">images/coach.jpg</div>
            <div>Career strategist professional headshot</div>
            <div class="specs">Recommended: 800x800px, JPG format, professional portrait with gradient border effect</div>
        </div>

        <div class="image-item">
            <div class="filename">images/banner.png</div>
            <div>Impact banner for From Hood to Hooded section</div>
            <div class="specs">Recommended: 1200x400px, PNG format, branded banner design</div>
        </div>

        <h2>Logo Files</h2>

        <div class="image-item">
            <div class="filename">images/blacktag.png</div>
            <div>From Hood to Hooded logo (light theme)</div>
            <div class="specs">Recommended: 300x100px, PNG format, transparent background</div>
        </div>

        <div class="image-item">
            <div class="filename">images/tagtp.png</div>
            <div>From Hood to Hooded logo (dark theme)</div>
            <div class="specs">Recommended: 300x100px, PNG format, transparent background</div>
        </div>

        <h2>Carousel Images</h2>

        <div class="image-item">
            <div class="filename">images/eva1.jpg</div>
            <div>Eva character image 1</div>
            <div class="specs">Recommended: 400x400px, JPG format, animated series character</div>
        </div>

        <div class="image-item">
            <div class="filename">images/eva2.jpg</div>
            <div>Eva character image 2</div>
            <div class="specs">Recommended: 400x400px, JPG format, animated series character</div>
        </div>

        <div class="image-item">
            <div class="filename">images/c1.jpg to images/c5.jpg</div>
            <div>Character/corporate images for carousel quotes</div>
            <div class="specs">Recommended: 400x400px each, JPG format, diverse professional women or corporate scenes</div>
        </div>

        <h2>Social Media Icons</h2>

        <div class="image-item">
            <div class="filename">images/youtube.png</div>
            <div>YouTube platform icon</div>
            <div class="specs">Recommended: 64x64px, PNG format, transparent background</div>
        </div>

        <div class="image-item">
            <div class="filename">images/instagram.png</div>
            <div>Instagram platform icon</div>
            <div class="specs">Recommended: 64x64px, PNG format, transparent background</div>
        </div>

        <div class="image-item">
            <div class="filename">images/tiktok.png</div>
            <div>TikTok platform icon</div>
            <div class="specs">Recommended: 64x64px, PNG format, transparent background</div>
        </div>

        <h2>Social Media Placeholders</h2>

        <div class="image-item">
            <div class="filename">images/instagram-placeholder-1.jpg to images/instagram-placeholder-3.jpg</div>
            <div>Instagram reel thumbnails</div>
            <div class="specs">Recommended: 300x533px (9:16 aspect ratio), JPG format</div>
        </div>

        <div class="image-item">
            <div class="filename">images/tiktok-placeholder-1.jpg to images/tiktok-placeholder-3.jpg</div>
            <div>TikTok video thumbnails</div>
            <div class="specs">Recommended: 300x533px (9:16 aspect ratio), JPG format</div>
        </div>

        <div class="note">
            <strong>Design Guidelines:</strong>
            <ul>
                <li>Use the brand colors: Gold (#D4AF37), Purple (#6B46C1), Rose (#F43F5E), Emerald (#10B981)</li>
                <li>Maintain professional, sophisticated aesthetic</li>
                <li>Images should represent Black women in corporate/professional settings</li>
                <li>All images will have gradient borders applied via CSS</li>
                <li>Consider glassmorphism and modern design elements</li>
            </ul>
        </div>
    </div>
</body>
</html>
