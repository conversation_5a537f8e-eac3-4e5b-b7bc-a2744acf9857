# Always Bank on Her - Website

A sophisticated, modern website for Always Bank on Her featuring the "From Hood to Hooded" animated series. Built with clean HTML, CSS, and JavaScript with glassmorphism effects, smooth animations, and responsive design.

## 🌟 Features

- **Modern Design**: Glassmorphism effects, gradient bento boxes, and sophisticated typography
- **Responsive**: Optimized for desktop, tablet, and mobile devices
- **Interactive**: Smooth scroll animations, carousel functionality, and theme switching
- **Social Media Integration**: Embedded YouTube, Instagram, and TikTok feeds
- **Accessibility**: Semantic HTML, keyboard navigation, and screen reader friendly
- **Performance**: Optimized images, efficient CSS, and smooth animations

## 🎨 Design Elements

- **Color Palette**: <PERSON> (#D4AF37), <PERSON> (#6B46C1), <PERSON> (#F43F5E), Emerald (#10B981)
- **Typography**: Inter (primary), Playfair Display (headings)
- **Effects**: Glassmorphism, gradient borders, smooth transitions
- **Layout**: CSS Grid, Flexbox, responsive design patterns

## 📁 File Structure

```
ABH/
├── index.html              # Main HTML file
├── styles.css              # Complete CSS with animations and responsive design
├── script.js               # JavaScript for interactivity and animations
├── image-requirements.html # Guide for required images
├── README.md               # This file
└── images/                 # Image assets directory (to be populated)
    ├── header.png          # Hero background
    ├── coach.jpg           # Strategist photo
    ├── banner.png          # Impact banner
    ├── blacktag.png        # Logo (light theme)
    ├── tagtp.png           # Logo (dark theme)
    ├── eva1.jpg            # Character image 1
    ├── eva2.jpg            # Character image 2
    ├── c1.jpg - c5.jpg     # Carousel images
    ├── youtube.png         # YouTube icon
    ├── instagram.png       # Instagram icon
    ├── tiktok.png          # TikTok icon
    └── *-placeholder-*.jpg # Social media thumbnails
```

## 🚀 Getting Started

1. **Clone or Download** the project files
2. **Add Images**: Place all required images in the `images/` directory (see `image-requirements.html`)
3. **Open**: Open `index.html` in a modern web browser
4. **Customize**: Edit content, colors, and images as needed

## 📱 Sections Overview

### Hero Section
- Dynamic header with background image
- Compelling headline and call-to-action
- Smooth scroll indicator

### About Section
- Two-column layout with strategist photo
- Key value propositions
- Interactive statement cards

### From Hood to Hooded Series
- Series branding and description
- Impact banner integration
- Theme-aware logo switching

### Carousel Section
- Inspirational quotes with character images
- Touch/swipe support for mobile
- Auto-play functionality

### Benefits Section
- Service offerings in card format
- Glassmorphism effects
- Hover animations

### Social Media Section
- YouTube, Instagram, and TikTok feeds
- Video thumbnails with play buttons
- Modal interactions for better UX

### Call-to-Action Sections
- Strategic placement throughout
- Consistent branding and messaging

## 🎯 Interactive Features

- **Theme Toggle**: Light/dark mode switching
- **Smooth Scrolling**: Animated navigation between sections
- **Scroll Animations**: Elements animate into view
- **Carousel**: Touch-enabled image/quote carousel
- **Mobile Menu**: Hamburger navigation for mobile
- **Video Modals**: Enhanced social media interaction
- **Responsive Images**: Optimized for all screen sizes

## 🛠️ Customization

### Colors
Edit CSS variables in `:root` section of `styles.css`:
```css
:root {
    --primary-gold: #D4AF37;
    --secondary-purple: #6B46C1;
    /* ... other colors */
}
```

### Content
- Update text content directly in `index.html`
- Modify quotes in the `CarouselManager` class in `script.js`
- Replace social media links in the `SocialMediaManager` class

### Images
- Follow the specifications in `image-requirements.html`
- Maintain aspect ratios for best results
- Use high-quality images for professional appearance

## 📊 Performance Optimization

- **CSS**: Efficient selectors, minimal reflows
- **JavaScript**: Event delegation, requestAnimationFrame
- **Images**: Lazy loading, fallback handling
- **Fonts**: Optimized Google Fonts loading

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📝 Content Guidelines

- **Professional Tone**: Sophisticated, empowering language
- **Inclusive**: Focused on Black women in corporate finance
- **Strategic**: Emphasizes career strategy over coaching
- **Authentic**: Real experiences and genuine storytelling

## 🔧 Technical Notes

- **CSS Grid & Flexbox**: Modern layout techniques
- **CSS Custom Properties**: Consistent theming
- **Intersection Observer**: Efficient scroll animations
- **Touch Events**: Mobile-friendly interactions
- **Local Storage**: Theme preference persistence

## 📞 Support

For technical questions or customization needs, refer to the code comments or contact the development team.

---

**Always Bank on Her** - Where Brilliance Meets Boardroom 👑
