// EmailJS Configuration File
// Replace these values with your actual EmailJS credentials

const EMAILJS_CONFIG = {
    // Your EmailJS Public Key (found in Account > General)
    publicKey: 'hoNV3GUknc98cvEVL',

    // Your EmailJS Service ID (found in Email Services)
    serviceId: 'service_s4exboe',

    // Your EmailJS Template ID (found in Email Templates)
    templateId: 'template_booking',

    // Optional: Confirmation email template ID (if you want to send confirmations to users)
    confirmationTemplateId: 'YOUR_CONFIRMATION_TEMPLATE_ID_HERE'
};

// Instructions:
// 1. Go to https://www.emailjs.com/ and create an account
// 2. Set up your email service (Gmail, Outlook, etc.)
// 3. Create an email template using the provided template below
// 4. Replace the values above with your actual EmailJS credentials
// 5. Update the script.js file with these values

/*
EMAIL TEMPLATE CONTENT FOR EMAILJS:

Subject: New Strategy Call Booking Request from {{firstName}} {{lastName}}

Body:
<h2>New Strategy Call Booking Request</h2>

<h3>Contact Information:</h3>
<p><strong>Name:</strong> {{firstName}} {{lastName}}</p>
<p><strong>Email:</strong> {{email}}</p>
<p><strong>Phone:</strong> {{phone}}</p>

<h3>Professional Information:</h3>
<p><strong>Company:</strong> {{company}}</p>
<p><strong>Position:</strong> {{position}}</p>
<p><strong>Experience:</strong> {{experience}}</p>

<h3>Strategy Information:</h3>
<p><strong>Primary Goal:</strong> {{goals}}</p>
<p><strong>Preferred Call Time:</strong> {{timeline}}</p>

<h3>Biggest Challenge:</h3>
<p>{{challenges}}</p>

<h3>Additional Information:</h3>
<p><strong>Communication Consent:</strong> {{consent}}</p>
<p><strong>Submission Date:</strong> {{submissionDate}}</p>

<hr>
<p><em>This booking request was submitted through the Always Bank on Her website.</em></p>
<p><em>User Agent: {{userAgent}}</em></p>

*/
