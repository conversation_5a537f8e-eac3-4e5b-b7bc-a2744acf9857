// Video Background Manager
class VideoManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupVideo();
    }

    setupVideo() {
        // Setup hero video
        const heroVideo = document.querySelector('.hero-video');
        if (heroVideo) {
            this.configureVideo(heroVideo, 'hero');
        }

        // Setup CTA video
        const ctaVideo = document.querySelector('.cta-video');
        if (ctaVideo) {
            this.configureVideo(ctaVideo, 'cta');
        }
    }

    configureVideo(video, type) {
        // Ensure video plays on mobile devices
        video.setAttribute('playsinline', '');
        video.setAttribute('webkit-playsinline', '');

        // Handle video loading
        video.addEventListener('loadeddata', () => {
            video.setAttribute('data-loaded', 'true');
            video.style.opacity = '1';
        });

        // Fallback for browsers that don't support autoplay
        video.addEventListener('canplay', () => {
            video.play().catch(e => {
                console.log(`${type} video autoplay failed:`, e);
                // Show fallback image if video fails
                const fallbackImg = video.nextElementSibling;
                if (fallbackImg && fallbackImg.tagName === 'IMG') {
                    fallbackImg.style.display = 'block';
                    fallbackImg.style.opacity = '0.3';
                    video.style.display = 'none';
                }
            });
        });

        // Handle video errors
        video.addEventListener('error', () => {
            console.log(`${type} video failed to load`);
            const fallbackImg = video.nextElementSibling;
            if (fallbackImg && fallbackImg.tagName === 'IMG') {
                fallbackImg.style.display = 'block';
                fallbackImg.style.opacity = '0.3';
                video.style.display = 'none';
            }
        });
    }
}

// Scroll Animation Manager
class ScrollAnimationManager {
    constructor() {
        this.elements = [];
        this.init();
    }

    init() {
        this.observeElements();
        this.bindScrollEvents();
    }

    observeElements() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Add animation classes to elements
        const animatedElements = document.querySelectorAll('.about-content, .benefit-card, .platform-section');
        animatedElements.forEach(el => {
            el.classList.add('animate-on-scroll');
            observer.observe(el);
        });
    }

    bindScrollEvents() {
        let ticking = false;
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }

    handleScroll() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero-bg');
        
        if (parallax) {
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        }

        // Navbar background opacity
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            const opacity = Math.min(scrolled / 100, 1);
            navbar.style.background = `rgba(255, 255, 255, ${0.1 + opacity * 0.8})`;
        }
    }
}

// Carousel Manager
class CarouselManager {
    constructor() {
        this.currentSlide = 0;
        this.slides = [];
        this.init();
    }

    init() {
        this.createSlides();
        this.bindEvents();
        this.autoPlay();
    }

    createSlides() {
        const quotes = [
            {
                text: "Black women are the only flowers expected to bloom while not being watered.",
                author: "Anonymous",
                image: "images/eva.jpg"
            },
            {
                text: "If you accept the call to leadership, you must be willing to be misunderstood, criticized, opposed, accused, and even rejected.",
                author: "Leadership Wisdom",
                image: "images/eva2.jpg"
            },
            {
                text: "MAYBE IF YOU WERE NICER TO ME I WOULDN'T HAVE TO ACT ON THE RACIAL STEREOTYPES I'VE INTERNALIZED ABOUT YOU.",
                author: "Eva's Truth",
                image: "images/c1.jpg"
            },
            {
                text: "If you are free, you need to free somebody else. If you have some power, then your job is to empower somebody else.",
                author: "Toni Morrison",
                image: "images/c2.jpg"
            },
            {
                text: "Diversity is a fact. Equity is a choice. Inclusion is an action. Belonging is an outcome.",
                author: "Corporate Reality",
                image: "images/c3.jpg"
            },
            {
                text: "Sit at the table of the dreamers, the go-getters, the star gazers, the warriors, the ones who fight for others, the conversation hits different. It's where you'll truly learn how to live.",
                author: "Sisterhood Wisdom",
                image: "images/c4.jpg"
            },
            {
                text: "You have to leave behind the 'comfortable' to experience the spectacular!",
                author: "Growth Mindset",
                image: "images/c5.jpg"
            }
        ];

        const track = document.querySelector('.carousel-track');
        if (!track) return;

        track.innerHTML = '';
        
        quotes.forEach((quote, index) => {
            const slide = document.createElement('div');
            slide.className = 'carousel-item';
            slide.innerHTML = `
                <img src="${quote.image}" alt="Inspiration ${index + 1}" class="carousel-image">
                <div class="carousel-quote">
                    <p class="quote-text">"${quote.text}"</p>
                    <p class="quote-author">— ${quote.author}</p>
                </div>
            `;
            track.appendChild(slide);
        });

        this.slides = document.querySelectorAll('.carousel-item');
        this.updateCarousel();
    }

    bindEvents() {
        const prevBtn = document.querySelector('.prev-btn');
        const nextBtn = document.querySelector('.next-btn');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevSlide());
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextSlide());
        }

        // Touch/swipe support
        let startX = 0;
        let endX = 0;

        const track = document.querySelector('.carousel-track');
        if (track) {
            track.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
            });

            track.addEventListener('touchend', (e) => {
                endX = e.changedTouches[0].clientX;
                this.handleSwipe();
            });
        }
    }

    handleSwipe() {
        const threshold = 50;
        const diff = startX - endX;

        if (Math.abs(diff) > threshold) {
            if (diff > 0) {
                this.nextSlide();
            } else {
                this.prevSlide();
            }
        }
    }

    nextSlide() {
        this.currentSlide = (this.currentSlide + 1) % this.slides.length;
        this.updateCarousel();
    }

    prevSlide() {
        this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
        this.updateCarousel();
    }

    updateCarousel() {
        const track = document.querySelector('.carousel-track');
        if (track) {
            track.style.transform = `translateX(-${this.currentSlide * 100}%)`;
        }
    }

    autoPlay() {
        setInterval(() => {
            this.nextSlide();
        }, 5000); // Change slide every 5 seconds
    }
}

// Social Media Manager
class SocialMediaManager {
    constructor() {
        this.init();
    }

    init() {
        this.createYouTubeEmbeds();
        this.createInstagramEmbeds();
        this.createTikTokEmbeds();
    }

    createYouTubeEmbeds() {
        const videos = [
            {
                url: 'https://youtube.com/shorts/1A4pRKf9_QM?feature=share',
                title: 'Eva\'s Corporate Journey',
                thumbnail: 'images/yt1.jpeg'
            },
            {
                url: 'https://www.youtube.com/shorts/riZQ3FYtdTg',
                title: 'Boardroom Strategies',
                thumbnail: 'images/yt2.jpeg'
            }
        ];

        const container = document.getElementById('youtube-feed');
        if (!container) return;

        container.innerHTML = '';

        videos.forEach((video, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'social-video-card youtube-card';
            thumbnail.innerHTML = `
                <div class="video-thumbnail-wrapper">
                    <img src="${video.thumbnail}"
                         alt="${video.title}"
                         class="video-thumbnail-img"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUzMyIgdmlld0JveD0iMCAwIDMwMCA1MzMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTMzIiBmaWxsPSIjRkYwMDAwIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiPllvdVR1YmU8L3RleHQ+Cjwvc3ZnPgo='">
                    <div class="play-button youtube-play">
                        <i class="fab fa-youtube"></i>
                    </div>
                    <div class="video-duration">Shorts</div>
                </div>
                <div class="video-info">
                    <h4 class="video-title">${video.title}</h4>
                    <p class="video-platform">YouTube Shorts</p>
                </div>
            `;

            thumbnail.addEventListener('click', () => {
                window.open(video.url, '_blank');
            });

            container.appendChild(thumbnail);
        });
    }

    createInstagramEmbeds() {
        const reels = [
            {
                url: 'https://www.instagram.com/alwaysbankonher/reel/DLD-GfzKIOf/',
                title: 'Corporate Code-Switching',
                thumbnail: 'images/in1.jpeg'
            },
            {
                url: 'https://www.instagram.com/alwaysbankonher/reel/DK70JekMwTV/',
                title: 'Boardroom Bias',
                thumbnail: 'images/in2.jpeg'
            },
            {
                url: 'https://www.instagram.com/alwaysbankonher/reel/DK70Ceqtcw7/',
                title: 'Executive Excellence',
                thumbnail: 'images/in3.jpeg'
            }
        ];

        const container = document.getElementById('instagram-feed');
        if (!container) return;

        container.innerHTML = '';

        reels.forEach((reel, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'social-video-card instagram-card';
            thumbnail.innerHTML = `
                <div class="video-thumbnail-wrapper">
                    <img src="${reel.thumbnail}"
                         alt="${reel.title}"
                         class="video-thumbnail-img"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUzMyIgdmlld0JveD0iMCAwIDMwMCA1MzMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTMzIiBmaWxsPSIjRTQzMDVCIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkluc3RhZ3JhbTwvdGV4dD4KPHN2Zz4K'">
                    <div class="play-button instagram-play">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <div class="video-duration">Reel</div>
                </div>
                <div class="video-info">
                    <h4 class="video-title">${reel.title}</h4>
                    <p class="video-platform">Instagram Reels</p>
                </div>
            `;

            thumbnail.addEventListener('click', () => {
                window.open(reel.url, '_blank');
            });

            container.appendChild(thumbnail);
        });
    }

    createTikTokEmbeds() {
        const videos = [
            {
                url: 'https://www.tiktok.com/@drjenniferrbishop/video/7515541921301826859',
                title: 'Career Strategy Secrets',
                thumbnail: 'images/tk1.jpeg'
            },
            {
                url: 'https://www.tiktok.com/@drjenniferrbishop/video/7516283939523317038',
                title: 'Executive Mindset',
                thumbnail: 'images/tk2.jpeg'
            },
            {
                url: 'https://www.tiktok.com/@drjenniferrbishop/video/7515171040759270702',
                title: 'Sisterhood Power',
                thumbnail: 'images/tk3.jpeg'
            }
        ];

        const container = document.getElementById('tiktok-feed');
        if (!container) return;

        container.innerHTML = '';

        videos.forEach((video, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'social-video-card tiktok-card';
            thumbnail.innerHTML = `
                <div class="video-thumbnail-wrapper">
                    <img src="${video.thumbnail}"
                         alt="${video.title}"
                         class="video-thumbnail-img"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUzMyIgdmlld0JveD0iMCAwIDMwMCA1MzMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTMzIiBmaWxsPSIjMDAwMDAwIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlRpa1RvazwvdGV4dD4KPHN2Zz4K'">
                    <div class="play-button tiktok-play">
                        <i class="fab fa-tiktok"></i>
                    </div>
                    <div class="video-duration">Video</div>
                </div>
                <div class="video-info">
                    <h4 class="video-title">${video.title}</h4>
                    <p class="video-platform">TikTok</p>
                </div>
            `;

            thumbnail.addEventListener('click', () => {
                window.open(video.url, '_blank');
            });

            container.appendChild(thumbnail);
        });
    }



    extractYouTubeId(url) {
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(regex);
        return match ? match[1] : '';
    }
}

// Navigation Manager
class NavigationManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.handleSmoothScroll();
    }

    bindEvents() {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        if (hamburger && navMenu) {
            hamburger.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                hamburger.classList.toggle('active');
            });
        }

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (navMenu) navMenu.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
            });
        });
    }

    handleSmoothScroll() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
}

// Booking Modal Manager
class BookingModalManager {
    constructor() {
        this.modal = document.getElementById('booking-modal');
        this.form = document.getElementById('strategy-call-form');

        // EmailJS Configuration - LIVE CREDENTIALS
        this.emailjsConfig = {
            publicKey: 'hoNV3GUknc98cvEVL',           // Your EmailJS public key
            serviceId: 'service_s4exboe',             // Your EmailJS service ID
            templateId: 'template_booking'            // Your EmailJS template ID
        };

        this.init();
    }

    init() {
        this.initEmailJS();
        this.bindEvents();
    }

    initEmailJS() {
        // Initialize EmailJS with your public key
        if (typeof emailjs !== 'undefined') {
            emailjs.init(this.emailjsConfig.publicKey);
        } else {
            console.error('EmailJS library not loaded');
        }
    }

    bindEvents() {
        // Open modal triggers
        const triggers = document.querySelectorAll('.booking-trigger');
        triggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                this.openModal();
            });
        });

        // Close modal triggers
        const closeBtn = document.getElementById('close-booking');
        const overlay = this.modal.querySelector('.modal-overlay');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }

        if (overlay) {
            overlay.addEventListener('click', () => this.closeModal());
        }

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('active')) {
                this.closeModal();
            }
        });

        // Form submission
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }
    }

    openModal() {
        this.modal.style.display = 'flex';
        // Trigger animation after display is set
        setTimeout(() => {
            this.modal.classList.add('active');
        }, 10);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus first input
        const firstInput = this.form.querySelector('input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 300);
        }
    }

    closeModal() {
        this.modal.classList.remove('active');

        // Hide modal after animation
        setTimeout(() => {
            this.modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }

    async handleSubmit(e) {
        e.preventDefault();

        const submitBtn = this.form.querySelector('.submit-btn');
        const btnText = submitBtn.querySelector('span:not(.btn-loading)');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        // Show loading state
        submitBtn.disabled = true;
        if (btnText) btnText.style.display = 'none';
        if (btnLoading) btnLoading.style.display = 'flex';

        try {
            // Collect form data
            const formData = new FormData(this.form);
            const data = Object.fromEntries(formData.entries());

            // Prepare template parameters for EmailJS
            const templateParams = {
                firstName: data.firstName || '',
                lastName: data.lastName || '',
                email: data.email || '',
                phone: data.phone || '',
                company: data.company || 'Not provided',
                position: data.position || '',
                experience: data.experience || '',
                goals: data.goals || '',
                challenges: data.challenges || '',
                timeline: data.timeline || '',
                consent: data.consent ? 'Yes, agreed to receive communications' : 'No consent provided',
                submissionDate: new Date().toLocaleString(),
                userAgent: navigator.userAgent
            };

            console.log('Sending email with data:', templateParams);

            // Send email via EmailJS
            const response = await emailjs.send(
                this.emailjsConfig.serviceId,
                this.emailjsConfig.templateId,
                templateParams
            );

            console.log('EmailJS response:', response);

            if (response.status === 200) {
                // Show success message
                this.showSuccessMessage(data);

                // Optional: Send confirmation email to user
                await this.sendConfirmationEmail(templateParams);
            } else {
                throw new Error('Email sending failed');
            }

        } catch (error) {
            console.error('Form submission error:', error);
            this.showErrorMessage(error);
        } finally {
            // Reset button state
            submitBtn.disabled = false;
            if (btnText) btnText.style.display = 'block';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }

    async sendConfirmationEmail(data) {
        try {
            // Optional: Send a confirmation email to the user
            // You would need to create a separate template for this
            const confirmationParams = {
                to_email: data.email,
                firstName: data.firstName,
                lastName: data.lastName,
                submissionDate: data.submissionDate
            };

            // Uncomment and configure if you want to send confirmation emails
            /*
            await emailjs.send(
                this.emailjsConfig.serviceId,
                'YOUR_CONFIRMATION_TEMPLATE_ID', // Create a separate template for confirmations
                confirmationParams
            );
            */

        } catch (error) {
            console.log('Confirmation email failed (non-critical):', error);
        }
    }

    showSuccessMessage(formData) {
        const container = this.modal.querySelector('.booking-form-container');
        container.innerHTML = `
            <div class="success-message">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>Booking Request Received!</h2>
                <p>Thank you, ${formData.firstName}! Your strategy call request has been successfully submitted. We'll be in touch within 24 hours to schedule your session.</p>
                <div class="success-actions">
                    <button class="cta-primary" onclick="document.querySelector('.booking-modal').classList.remove('active'); setTimeout(() => { document.querySelector('.booking-modal').style.display = 'none'; document.body.style.overflow = ''; }, 300);">
                        Continue Exploring
                    </button>
                </div>
                <div class="next-steps">
                    <h3>What happens next?</h3>
                    <ul>
                        <li>We'll review your information and career goals</li>
                        <li>You'll receive a calendar link to book your preferred time</li>
                        <li>We'll send you a pre-call questionnaire to maximize our time</li>
                        <li>Your 15-minute strategy session will be scheduled and confirmed</li>
                    </ul>
                </div>
                <div class="contact-info">
                    <p><strong>Questions?</strong> Reply to the confirmation email or contact us directly.</p>
                </div>
            </div>
        `;
    }

    showErrorMessage(error) {
        const container = this.modal.querySelector('.booking-form-container');
        container.innerHTML = `
            <div class="error-message">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2>Submission Error</h2>
                <p>We're sorry, but there was an issue submitting your booking request. Please try again or contact us directly.</p>
                <div class="error-details">
                    <p><strong>What you can do:</strong></p>
                    <ul>
                        <li>Check your internet connection and try again</li>
                        <li>Email us directly at: <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li>Call us for immediate assistance</li>
                    </ul>
                </div>
                <div class="error-actions">
                    <button class="cta-primary" onclick="location.reload();">
                        Try Again
                    </button>
                    <button class="cta-secondary" onclick="document.querySelector('.booking-modal').classList.remove('active'); setTimeout(() => { document.querySelector('.booking-modal').style.display = 'none'; document.body.style.overflow = ''; }, 300);">
                        Close
                    </button>
                </div>
            </div>
        `;

        // Log error for debugging
        console.error('Booking form error:', error);
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new VideoManager();
    new ScrollAnimationManager();
    new CarouselManager();
    new SocialMediaManager();
    new NavigationManager();
    new BookingModalManager();

    // Add loading animation
    document.body.classList.add('loaded');
});
