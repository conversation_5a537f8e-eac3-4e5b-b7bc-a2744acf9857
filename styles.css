/* CSS Variables for Design System */
:root {
    /* Colors */
    --primary-gold: #D4AF37;
    --primary-gold-light: #E6C866;
    --primary-gold-dark: #B8941F;
    --secondary-purple: #6B46C1;
    --secondary-purple-light: #8B5CF6;
    --accent-rose: #F43F5E;
    --accent-emerald: #10B981;
    
    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-primary: 'Nunito', sans-serif;
    --font-display: '<PERSON>eist', sans-serif;
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    --space-3xl: 6rem;
    
    /* Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 1rem;
    --radius-lg: 1.5rem;
    --radius-xl: 2rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-backdrop: blur(20px);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-purple) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--accent-rose) 0%, var(--accent-emerald) 100%);
    --gradient-subtle: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    --gradient-dark: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
}



/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--white);
    overflow-x: hidden;
    transition: all 0.3s ease;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--space-sm);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: var(--space-xl);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}



/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border-bottom: 1px solid var(--glass-border);
    z-index: 999;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 120px;
    min-height: 120px;
}

.logo-img {
    height: 100px;
    width: auto;
    max-width: 300px;
    object-fit: contain;
    filter: brightness(1.1) contrast(1.1);
    transition: all 0.3s ease;
}

.logo-img:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    gap: var(--space-lg);
}

.nav-link {
    text-decoration: none;
    background: linear-gradient(135deg, #FFFFFF 0%, #C0C0C0 25%, #FFFFFF 50%, #C0C0C0 75%, #FFFFFF 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 500;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    animation: silverShimmer 4s ease-in-out infinite;
}

.nav-link:hover {
    background: linear-gradient(135deg, #D4AF37 0%, #FFFFFF 25%, #D4AF37 50%, #FFFFFF 75%, #D4AF37 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: goldShimmer 2s ease-in-out infinite;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 120vh;
    min-height: 900px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-video[data-loaded="true"] {
    opacity: 1;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.6) 0%,
        rgba(107, 70, 193, 0.4) 30%,
        rgba(212, 175, 55, 0.3) 70%,
        rgba(0, 0, 0, 0.5) 100%
    );
    z-index: 1;
}

.hero-content {
    text-align: center;
    max-width: 1000px;
    padding: 0 var(--space-md);
    animation: fadeInUp 1s ease-out;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 6rem;
    font-weight: 900;
    margin-bottom: var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    letter-spacing: -0.02em;
    line-height: 0.9;
}

.title-line {
    background: linear-gradient(
        135deg,
        #FFFFFF 0%,
        #D4AF37 25%,
        #FFFFFF 50%,
        #D4AF37 75%,
        #FFFFFF 100%
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.8));
    position: relative;
    animation: shimmer 3s ease-in-out infinite;
}

.title-line::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(212,175,55,0.2));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: -1;
    transform: translate(2px, 2px);
}

.title-subtitle {
    font-size: 2.5rem;
    color: var(--white);
    font-weight: 600;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
    letter-spacing: -0.01em;
    margin-top: var(--space-sm);
}

.hero-description {
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: var(--space-xl);
    line-height: 1.7;
    font-weight: 500;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    background: rgba(0, 0, 0, 0.3);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-primary {
    display: inline-block;
    padding: var(--space-md) var(--space-xl);
    background: var(--gradient-primary);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.glass-button {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.cta-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.cta-primary:hover::before {
    left: 100%;
}

.cta-subtitle {
    display: block;
    font-size: 0.9rem;
    font-weight: 400;
    margin-top: var(--space-xs);
    opacity: 0.9;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--space-xl);
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
    z-index: 3;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: translateX(-50%) scale(1.1);
    animation-play-state: paused;
}

.scroll-arrow {
    width: 35px;
    height: 35px;
    border: 3px solid var(--white);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* About Section */
.about {
    padding: var(--space-3xl) 0;
    background: var(--gradient-subtle);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
}

.image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    background: var(--gradient-primary);
    padding: 4px;
}

.strategist-image {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    display: block;
}

.about-text {
    animation: slideInRight 1s ease-out;
}

.about-intro {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: var(--space-lg);
    color: var(--gray-600);
}

.key-statements {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.statement-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.statement-item:hover {
    transform: translateX(10px);
    background: var(--primary-gold);
    color: var(--white);
}

.statement-item i {
    font-size: 1.5rem;
    color: var(--primary-gold);
    transition: color 0.3s ease;
}

.statement-item:hover i {
    color: var(--white);
}

/* Eva's Survival Strategies Freebie Section */
.freebie-section {
    padding: var(--space-3xl) 0;
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.freebie-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(212,175,55,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(107,70,193,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.freebie-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.freebie-image {
    text-align: center;
}

.eva-image-container {
    position: relative;
    display: inline-block;
    max-width: 400px;
}

.eva-talk-image {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 3px solid;
    border-image: var(--gradient-primary) 1;
    position: relative;
    z-index: 2;
}

.image-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    filter: blur(20px);
    opacity: 0.3;
    z-index: 1;
    animation: pulse 3s ease-in-out infinite;
}

.freebie-text {
    max-width: 600px;
}

.freebie-badge {
    display: inline-block;
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-md);
    text-transform: uppercase;
}

.freebie-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: var(--space-sm);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
}

.freebie-subtitle {
    font-size: 1.5rem;
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: var(--space-lg);
    font-style: italic;
}

.freebie-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--gray-300);
    margin-bottom: var(--space-xl);
}

.strategy-highlights {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-md);
    margin-bottom: var(--space-xl);
}

.strategy-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    border: 1px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
}

.strategy-item:hover {
    background: rgba(212, 175, 55, 0.1);
    transform: translateX(5px);
}

.strategy-item i {
    color: var(--primary-gold);
    font-size: 1.2rem;
}

.strategy-item span {
    font-weight: 600;
    color: var(--white);
}

.freebie-cta {
    text-align: left;
}

.freebie-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-lg) var(--space-xl);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 2px solid var(--primary-gold);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    flex-direction: column;
    text-align: center;
}

.freebie-btn:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.3);
}

.freebie-btn i {
    font-size: 1.3rem;
}

.btn-subtitle {
    font-size: 0.8rem;
    font-weight: 400;
    opacity: 0.9;
    margin-top: var(--space-xs);
}

.freebie-note {
    margin-top: var(--space-md);
    font-size: 0.9rem;
    color: var(--gray-400);
    font-style: italic;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.05);
    }
}

/* Series Section */
.series {
    padding: var(--space-3xl) 0;
    background: var(--white);
}

.series-header {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.series-title {
    font-size: 3rem;
    margin-bottom: var(--space-sm);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.series-subtitle {
    font-size: 1.5rem;
    color: var(--gray-600);
    margin-bottom: var(--space-md);
    font-style: italic;
}

.series-description {
    font-size: 1.1rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.banner-section {
    margin-top: var(--space-xl);
    text-align: center;
}

.impact-banner {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Carousel Section */
.carousel-section {
    padding: var(--space-3xl) 0;
    background: var(--gradient-dark);
    color: var(--white);
}

.carousel-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: var(--radius-xl);
}

.carousel-track {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-item {
    min-width: 100%;
    display: flex;
    align-items: center;
    gap: var(--space-xl);
    padding: var(--space-xl);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
}

.carousel-image {
    width: 300px;
    height: 300px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    border: 3px solid;
    border-image: var(--gradient-primary) 1;
}

.carousel-quote {
    flex: 1;
}

.quote-text {
    font-size: 1.5rem;
    font-style: italic;
    line-height: 1.6;
    margin-bottom: var(--space-md);
    color: var(--white);
}

.quote-author {
    font-size: 1rem;
    color: var(--primary-gold);
    font-weight: 600;
}

.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--primary-gold);
    color: var(--white);
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 10;
}

.carousel-btn:hover {
    background: var(--primary-gold-light);
    transform: translateY(-50%) scale(1.1);
}

.prev-btn {
    left: var(--space-md);
}

.next-btn {
    right: var(--space-md);
}

/* Benefits Section */
.benefits {
    padding: var(--space-3xl) 0;
    background: var(--gradient-subtle);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-xl);
}

.benefit-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
}

.benefit-card h3 {
    font-size: 1.5rem;
    margin-bottom: var(--space-md);
    color: var(--gray-800);
}

.benefit-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Social Media Section */
.social-media {
    padding: var(--space-3xl) 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

.social-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    margin-top: var(--space-sm);
    font-weight: 500;
}

/* Featured Platform (YouTube) */
.featured-platform {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    margin-bottom: var(--space-3xl);
    box-shadow: var(--shadow-lg);
}

.platform-header-large {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 2px solid var(--glass-border);
}

.platform-icon-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.platform-icon-large {
    width: 60px;
    height: 60px;
}

.platform-icon-medium {
    width: 45px;
    height: 45px;
}

.platform-header-large h3 {
    font-size: 2rem;
    margin: 0;
    color: var(--gray-800);
}

.platform-header-medium h3 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--gray-800);
}

/* Featured Videos Layout */
.featured-videos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
}

/* Secondary Platforms */
.secondary-platforms {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xl);
    margin-bottom: var(--space-3xl);
}

.platform-column {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    box-shadow: var(--shadow-md);
}

.platform-header-medium {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--glass-border);
}

.platform-videos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-md);
}

/* Social Video Cards */
.social-video-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.social-video-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.youtube-card:hover {
    border-color: #FF0000;
}

.instagram-card:hover {
    border-color: #E4305B;
}

.tiktok-card:hover {
    border-color: #000000;
}

.video-thumbnail-wrapper {
    position: relative;
    aspect-ratio: 9/16;
    overflow: hidden;
}

.video-thumbnail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.social-video-card:hover .video-thumbnail-img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.youtube-play {
    background: rgba(255, 0, 0, 0.8);
}

.instagram-play {
    background: rgba(228, 48, 91, 0.8);
}

.tiktok-play {
    background: rgba(0, 0, 0, 0.8);
}

.social-video-card:hover .play-button {
    transform: translate(-50%, -50%) scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: var(--space-xs);
    right: var(--space-xs);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.video-info {
    padding: var(--space-md);
}

.video-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 var(--space-xs) 0;
    line-height: 1.3;
}

.video-platform {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
    font-weight: 500;
}

.video-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--space-md);
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.video-thumbnail:hover .video-overlay {
    transform: translateY(0);
}

.video-title {
    font-size: 0.9rem;
    font-weight: 600;
}



@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes silverShimmer {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes goldShimmer {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Platform Follow Buttons */
.platform-follow-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-lg);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    color: var(--white);
}

.youtube-btn {
    background: linear-gradient(135deg, #FF0000, #CC0000);
}

.instagram-btn {
    background: linear-gradient(135deg, #E4305B, #C13584);
}

.tiktok-btn {
    background: linear-gradient(135deg, #000000, #333333);
}

.platform-follow-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.youtube-btn:hover {
    background: linear-gradient(135deg, #FF3333, #FF0000);
}

.instagram-btn:hover {
    background: linear-gradient(135deg, #F56565, #E4305B);
}

.tiktok-btn:hover {
    background: linear-gradient(135deg, #333333, #000000);
}

/* Social CTA Section */
.social-cta {
    text-align: center;
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--space-3xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

.social-cta h3 {
    font-size: 2rem;
    margin-bottom: var(--space-md);
    color: var(--white);
}

.social-cta p {
    font-size: 1.1rem;
    margin-bottom: var(--space-xl);
    opacity: 0.9;
}

.social-cta .cta-primary {
    background: var(--white);
    color: var(--primary-gold);
    font-weight: 700;
}

.social-cta .cta-primary:hover {
    background: var(--gray-100);
    transform: translateY(-3px);
}

/* CTA Section */
.cta-section {
    padding: var(--space-3xl) 0;
    position: relative;
    color: var(--white);
    text-align: center;
    overflow: hidden;
}

.cta-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.cta-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.cta-video[data-loaded="true"] {
    opacity: 1;
}

.cta-fallback-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.3;
}

.cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(107, 70, 193, 0.2) 30%,
        rgba(212, 175, 55, 0.1) 70%,
        rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 1;
}

.cta-content {
    position: relative;
    z-index: 2;
    background: rgba(0, 0, 0, 0.01);
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.8rem;
    margin-bottom: var(--space-md);
    color: var(--white);
    text-shadow:
        3px 3px 6px rgba(0, 0, 0, 0.9),
        1px 1px 3px rgba(0, 0, 0, 0.8),
        0 0 10px rgba(0, 0, 0, 0.7);
    font-weight: 800;
    letter-spacing: -0.02em;
}

.cta-content p {
    font-size: 1.3rem;
    margin-bottom: var(--space-xl);
    color: var(--white);
    text-shadow:
        2px 2px 4px rgba(0, 0, 0, 0.9),
        1px 1px 2px rgba(0, 0, 0, 0.8),
        0 0 8px rgba(0, 0, 0, 0.6);
    line-height: 1.6;
    font-weight: 600;
}

.cta-section .cta-primary {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 2px solid var(--primary-gold);
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.cta-section .cta-primary:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.3);
}

/* Footer */
.footer {
    padding: var(--space-3xl) 0 var(--space-lg);
    background: var(--gray-900);
    color: var(--white);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-xl);
    align-items: center;
    margin-bottom: var(--space-xl);
}

.footer-text h3 {
    font-size: 1.8rem;
    margin-bottom: var(--space-sm);
    color: var(--primary-gold);
}

.footer-text p {
    color: var(--gray-300);
    font-size: 1.1rem;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-lg);
    border-top: 1px solid var(--gray-700);
}

.footer-bottom p {
    color: var(--gray-400);
}

.social-links {
    display: flex;
    gap: var(--space-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-gold);
    transform: translateY(-2px);
}

/* Mobile Navigation */
.nav-menu.active {
    display: flex;
    position: fixed;
    top: 120px;
    left: 0;
    width: 100%;
    height: calc(100vh - 120px);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-xl);
    font-size: 1.4rem;
    z-index: 998;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Loading Animation */
body:not(.loaded) {
    overflow: hidden;
}

body:not(.loaded)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

body:not(.loaded)::after {
    content: '';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10001;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--space-lg);
    }

    .hero {
        height: 100vh;
        min-height: 700px;
    }

    .hero-title {
        font-size: 4.5rem;
    }

    .title-subtitle {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1.2rem;
        padding: var(--space-md);
    }

    .benefits-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero {
        height: 100vh;
        min-height: 600px;
    }

    .hero-title {
        font-size: 3.5rem;
        gap: var(--space-sm);
    }

    .title-subtitle {
        font-size: 1.8rem;
    }

    .hero-description {
        font-size: 1.1rem;
        padding: var(--space-md);
        line-height: 1.6;
    }

    /* Mobile Navigation Improvements */
    .nav-container {
        padding: 0 var(--space-md);
    }

    .logo-img {
        height: 70px;
        max-width: 200px;
    }

    /* Mobile CTA Button Improvements */
    .cta-primary {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
        min-height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .glass-button {
        backdrop-filter: blur(15px);
        border-width: 2px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .freebie-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
        text-align: center;
    }

    .freebie-title {
        font-size: 2.5rem;
    }

    .strategy-highlights {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .series-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .carousel-item {
        flex-direction: column;
        text-align: center;
        padding: var(--space-lg);
    }

    .carousel-image {
        width: 250px;
        height: 250px;
    }

    .quote-text {
        font-size: 1.2rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .secondary-platforms {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .featured-videos {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-lg);
    }

    .platform-videos {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-sm);
    }

    .platform-header-large {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .platform-header-medium {
        flex-direction: column;
        gap: var(--space-sm);
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-lg);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .modal-content {
        width: 95%;
        margin: var(--space-md);
    }

    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .hero {
        height: 100vh;
        min-height: 500px;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        gap: var(--space-xs);
    }

    .title-subtitle {
        font-size: 1.3rem;
    }

    .hero-description {
        font-size: 1rem;
        padding: var(--space-sm);
        line-height: 1.5;
    }

    .hero-content {
        max-width: 100%;
        padding: 0 var(--space-sm);
    }

    /* Touch-friendly Navigation */
    .nav-container {
        height: 80px;
        min-height: 80px;
        padding: 0 var(--space-sm);
    }

    .logo-img {
        height: 60px;
        max-width: 180px;
    }

    .hamburger {
        width: 44px;
        height: 44px;
        padding: var(--space-sm);
    }

    .nav-menu.active {
        top: 80px;
        height: calc(100vh - 80px);
        font-size: 1.2rem;
        padding: var(--space-lg);
    }

    .nav-link {
        padding: var(--space-md);
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .series-title {
        font-size: 1.8rem;
    }

    .carousel-image {
        width: 200px;
        height: 200px;
    }

    .quote-text {
        font-size: 1rem;
    }

    .benefit-card {
        padding: var(--space-lg);
    }

    .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .featured-videos {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .platform-videos {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .featured-platform,
    .platform-column {
        padding: var(--space-lg);
    }

    .social-cta {
        padding: var(--space-xl);
    }

    .social-cta h3 {
        font-size: 1.5rem;
    }

    .platform-header-large h3 {
        font-size: 1.5rem;
    }

    .platform-header-medium h3 {
        font-size: 1.2rem;
    }

    .freebie-title {
        font-size: 2rem;
    }

    .freebie-subtitle {
        font-size: 1.2rem;
    }

    .eva-image-container {
        max-width: 300px;
    }

    .freebie-btn {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    .booking-form-content {
        grid-template-columns: 1fr;
    }

    .form-benefits {
        border-right: none;
        border-bottom: 1px solid var(--gray-200);
        padding: var(--space-lg);
    }

    .booking-form {
        padding: var(--space-lg);
    }

    .booking-form-header h2 {
        font-size: 1.5rem;
    }

    /* Mobile Modal Improvements */
    .booking-form-container {
        width: 95%;
        max-width: none;
        margin: var(--space-sm);
        max-height: 95vh;
    }

    .booking-form-header {
        padding: var(--space-lg);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: var(--space-md);
        min-height: 44px;
    }

    .submit-btn {
        min-height: 50px;
        font-size: 1.1rem;
        padding: var(--space-lg);
    }

    .close-booking-modal {
        width: 44px;
        height: 44px;
        font-size: 1.3rem;
    }

    .cta-primary {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    .cta-content h2 {
        font-size: 1.8rem;
    }

    .cta-content p {
        font-size: 1.1rem;
    }

    .cta-content {
        padding: var(--space-lg);
    }
}

/* Extra Small Mobile Devices (320px and below) */
@media (max-width: 320px) {
    .container {
        padding: 0 var(--space-sm);
    }

    .hero-title {
        font-size: 2rem;
    }

    .title-subtitle {
        font-size: 1.1rem;
    }

    .hero-description {
        font-size: 0.9rem;
        padding: var(--space-xs);
    }

    .section-title {
        font-size: 1.5rem;
    }

    .freebie-title {
        font-size: 1.8rem;
    }

    .cta-content h2 {
        font-size: 1.5rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .nav-container {
        height: 80px;
        min-height: 80px;
    }

    .logo-img {
        height: 60px;
        max-width: 180px;
    }

    .nav-menu.active {
        top: 80px;
        height: calc(100vh - 80px);
        font-size: 1.2rem;
    }
}

/* Portrait Orientation Specific */
@media (orientation: portrait) and (max-width: 768px) {
    .hero {
        height: 100vh;
        min-height: 600px;
    }

    .hero-content {
        padding: 0 var(--space-md);
    }

    .freebie-content {
        padding: var(--space-md) 0;
    }

    .eva-image-container {
        max-width: 280px;
        margin: 0 auto;
    }

    .strategy-highlights {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .carousel-item {
        padding: var(--space-md);
    }

    .social-video-card {
        margin-bottom: var(--space-sm);
    }

    .platform-videos {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .featured-videos {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

/* Landscape Mobile (Small Height) */
@media (orientation: landscape) and (max-height: 500px) {
    .hero {
        height: 100vh;
        min-height: 400px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .title-subtitle {
        font-size: 1.3rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: var(--space-md);
    }

    .nav-container {
        height: 70px;
        min-height: 70px;
    }

    .logo-img {
        height: 50px;
    }

    .nav-menu.active {
        top: 70px;
        height: calc(100vh - 70px);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image,
    .strategist-image,
    .carousel-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Booking Modal */
.booking-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.booking-modal.active {
    display: flex;
    opacity: 1;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.booking-form-container {
    position: relative;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    margin: auto;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: slideInUp 0.4s ease-out;
}

.booking-form-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--space-xl);
    text-align: center;
    position: relative;
}

.booking-form-header h2 {
    font-size: 2rem;
    margin-bottom: var(--space-sm);
    color: var(--white);
}

.booking-form-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.close-booking-modal {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.close-booking-modal:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.booking-form-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    max-height: calc(90vh - 120px);
    overflow: hidden;
}

.form-benefits {
    background: var(--gray-50);
    padding: var(--space-xl);
    border-right: 1px solid var(--gray-200);
}

.form-benefits h3 {
    font-size: 1.3rem;
    color: var(--gray-800);
    margin-bottom: var(--space-lg);
}

.benefit-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
}

.benefit-item i {
    color: var(--primary-gold);
    font-size: 1.1rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.benefit-item span {
    color: var(--gray-700);
    line-height: 1.5;
    font-weight: 500;
}

.booking-form {
    padding: var(--space-xl);
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-xs);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: var(--font-primary);
    transition: all 0.3s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.checkbox-group {
    margin-bottom: var(--space-xl);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-gold);
    border-color: var(--primary-gold);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-weight: bold;
    font-size: 0.8rem;
}

.submit-btn {
    width: 100%;
    padding: var(--space-lg);
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.form-note {
    text-align: center;
    font-size: 0.8rem;
    color: var(--gray-500);
    margin-top: var(--space-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.form-note i {
    color: var(--primary-gold);
}

/* Success Message */
.success-message {
    padding: var(--space-3xl);
    text-align: center;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

.success-icon {
    font-size: 4rem;
    color: var(--accent-emerald);
    margin-bottom: var(--space-lg);
}

.success-message h2 {
    font-size: 2rem;
    color: var(--gray-800);
    margin-bottom: var(--space-md);
}

.success-message > p {
    font-size: 1.1rem;
    color: var(--gray-600);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

.success-actions {
    margin-bottom: var(--space-xl);
}

.next-steps {
    background: var(--gray-50);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    text-align: left;
    max-width: 500px;
    margin: 0 auto;
}

.next-steps h3 {
    color: var(--gray-800);
    margin-bottom: var(--space-md);
    text-align: center;
}

.next-steps ul {
    list-style: none;
    padding: 0;
}

.next-steps li {
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--gray-200);
    position: relative;
    padding-left: var(--space-lg);
}

.next-steps li:last-child {
    border-bottom: none;
}

.next-steps li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: var(--space-sm);
    color: var(--accent-emerald);
    font-weight: bold;
}

.contact-info {
    margin-top: var(--space-lg);
    padding: var(--space-md);
    background: var(--gray-100);
    border-radius: var(--radius-md);
    text-align: center;
    font-size: 0.9rem;
    color: var(--gray-600);
}

/* Error Message */
.error-message {
    padding: var(--space-3xl);
    text-align: center;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

.error-icon {
    font-size: 4rem;
    color: var(--accent-rose);
    margin-bottom: var(--space-lg);
}

.error-message h2 {
    font-size: 2rem;
    color: var(--gray-800);
    margin-bottom: var(--space-md);
}

.error-message > p {
    font-size: 1.1rem;
    color: var(--gray-600);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

.error-details {
    background: var(--gray-50);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    text-align: left;
    max-width: 500px;
    margin: 0 auto var(--space-xl);
}

.error-details ul {
    list-style: none;
    padding: 0;
    margin-top: var(--space-md);
}

.error-details li {
    padding: var(--space-xs) 0;
    position: relative;
    padding-left: var(--space-lg);
}

.error-details li::before {
    content: '•';
    position: absolute;
    left: 0;
    top: var(--space-xs);
    color: var(--accent-rose);
    font-weight: bold;
}

.error-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-secondary {
    padding: var(--space-md) var(--space-lg);
    background: var(--gray-200);
    color: var(--gray-700);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cta-secondary:hover {
    background: var(--gray-300);
    transform: translateY(-1px);
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Touch-friendly button sizes */
    .cta-primary,
    .platform-follow-btn,
    .freebie-btn {
        min-height: 44px;
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    /* Touch-friendly social icons */
    .social-links a {
        width: 48px;
        height: 48px;
        font-size: 1.2rem;
    }

    /* Touch-friendly video cards */
    .social-video-card {
        min-height: 44px;
    }

    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    /* Touch-friendly carousel controls */
    .carousel-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Remove hover effects on touch devices */
    .social-video-card:hover,
    .benefit-card:hover,
    .strategy-item:hover {
        transform: none;
    }

    /* Ensure tap targets are large enough */
    .nav-link,
    .hamburger,
    .close-booking-modal {
        min-width: 44px;
        min-height: 44px;
    }
}

/* High DPI Mobile Displays */
@media (-webkit-min-device-pixel-ratio: 2) and (max-width: 768px) {
    .hero-video,
    .cta-video,
    .logo-img,
    .eva-talk-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hamburger,
    .scroll-indicator,
    .carousel-btn,
    .booking-modal {
        display: none !important;
    }

    .hero {
        height: auto;
        padding: var(--space-lg) 0;
    }

    .section {
        page-break-inside: avoid;
    }
}
