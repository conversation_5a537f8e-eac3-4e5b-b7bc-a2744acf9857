# EmailJS Integration Setup Guide

## 🚀 **Complete Step-by-Step Setup**

### **Step 1: Create EmailJS Account**
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Click "Sign Up" and create your free account
3. Verify your email address
4. Log into your EmailJS dashboard

### **Step 2: Set Up Email Service**

#### **For Gmail (Recommended):**
1. In EmailJS dashboard, click **"Email Services"**
2. Click **"Add New Service"**
3. Select **"Gmail"**
4. Click **"Connect Account"** and authorize EmailJS
5. Give your service a name: `"Always Bank on Her Bookings"`
6. **Copy the Service ID** (e.g., `service_abc123`) - you'll need this!

#### **For Other Email Providers:**
- **Outlook:** Select "Outlook" and follow OAuth setup
- **Yahoo:** Select "Yahoo" and follow OAuth setup
- **Custom SMTP:** Use your email provider's SMTP settings

### **Step 3: Create Email Template**

1. Click **"Email Templates"** in the dashboard
2. Click **"Create New Template"**
3. **Template Name:** `Strategy Call Booking Request`
4. **Template ID:** Copy this ID (e.g., `template_xyz789`)

#### **Template Configuration:**

**To Email:** `<EMAIL>` (your business email)
**From Name:** `{{firstName}} {{lastName}}`
**Reply To:** `{{email}}`
**Subject:** `New Strategy Call Booking Request from {{firstName}} {{lastName}}`

**Email Body (HTML):**
```html
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #D4AF37; border-bottom: 2px solid #D4AF37; padding-bottom: 10px;">
        New Strategy Call Booking Request
    </h2>

    <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3 style="color: #6B46C1; margin-top: 0;">Contact Information</h3>
        <p><strong>Name:</strong> {{firstName}} {{lastName}}</p>
        <p><strong>Email:</strong> <a href="mailto:{{email}}">{{email}}</a></p>
        <p><strong>Phone:</strong> <a href="tel:{{phone}}">{{phone}}</a></p>
    </div>

    <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3 style="color: #6B46C1; margin-top: 0;">Professional Information</h3>
        <p><strong>Company:</strong> {{company}}</p>
        <p><strong>Position:</strong> {{position}}</p>
        <p><strong>Experience:</strong> {{experience}}</p>
    </div>

    <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3 style="color: #6B46C1; margin-top: 0;">Strategy Information</h3>
        <p><strong>Primary Goal:</strong> {{goals}}</p>
        <p><strong>Preferred Call Time:</strong> {{timeline}}</p>
    </div>

    <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3 style="color: #6B46C1; margin-top: 0;">Biggest Challenge</h3>
        <p style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #D4AF37;">
            {{challenges}}
        </p>
    </div>

    <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 8px; font-size: 14px;">
        <p><strong>Communication Consent:</strong> {{consent}}</p>
        <p><strong>Submission Date:</strong> {{submissionDate}}</p>
    </div>

    <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
    
    <div style="text-align: center; color: #666; font-size: 12px;">
        <p>This booking request was submitted through the Always Bank on Her website.</p>
        <p>Always Bank on Her - Where Brilliance Meets Boardroom</p>
    </div>
</div>
```

5. Click **"Save"** to create the template

### **Step 4: Get Your Public Key**

1. Go to **"Account"** → **"General"**
2. Find your **Public Key** (looks like `user_abc123xyz`)
3. **Copy this key** - you'll need it!

### **Step 5: Update Your Website**

#### **Method 1: Direct Update (Recommended)**
Open your `script.js` file and find this section around line 460:

```javascript
// EmailJS Configuration - REPLACE THESE WITH YOUR ACTUAL VALUES
this.emailjsConfig = {
    publicKey: 'YOUR_PUBLIC_KEY_HERE',        // Replace with your EmailJS public key
    serviceId: 'YOUR_SERVICE_ID_HERE',        // Replace with your EmailJS service ID
    templateId: 'YOUR_TEMPLATE_ID_HERE'       // Replace with your EmailJS template ID
};
```

**Replace with your actual values:**
```javascript
this.emailjsConfig = {
    publicKey: 'user_abc123xyz',              // Your actual public key
    serviceId: 'service_abc123',              // Your actual service ID
    templateId: 'template_xyz789'             // Your actual template ID
};
```

#### **Method 2: Using Configuration File**
1. Update the `emailjs-setup.js` file with your credentials
2. Include it in your HTML before `script.js`:
```html
<script src="emailjs-setup.js"></script>
<script src="script.js"></script>
```

### **Step 6: Test Your Setup**

1. **Save all files** and refresh your website
2. **Click any booking button** to open the form
3. **Fill out the form** with test data
4. **Submit the form**
5. **Check your email** for the booking notification

#### **Testing Checklist:**
- ✅ Form opens when clicking CTA buttons
- ✅ All form fields are working
- ✅ Form submits without errors
- ✅ You receive the email notification
- ✅ Email contains all form data correctly
- ✅ Success message appears after submission

### **Step 7: Optional Enhancements**

#### **Auto-Reply Template (Optional):**
Create a second template to send confirmations to users:

1. Create new template: `Booking Confirmation`
2. **To Email:** `{{email}}`
3. **From Name:** `Always Bank on Her`
4. **Subject:** `Your Strategy Call Request - We'll Be In Touch Soon!`

**Confirmation Email Body:**
```html
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #D4AF37;">Thank You, {{firstName}}!</h2>
    
    <p>We've received your strategy call request and are excited to help you advance your career.</p>
    
    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>What happens next?</h3>
        <ol>
            <li>We'll review your information and goals</li>
            <li>You'll receive a calendar link within 24 hours</li>
            <li>We'll send you a pre-call questionnaire</li>
            <li>Your 15-minute strategy session will be scheduled</li>
        </ol>
    </div>
    
    <p>Questions? Simply reply to this email.</p>
    
    <p>Best regards,<br>
    <strong>Always Bank on Her Team</strong></p>
</div>
```

### **Step 8: Monitor and Maintain**

#### **EmailJS Dashboard Monitoring:**
- Check your **usage statistics** monthly
- Monitor **delivery rates**
- Review **error logs** if issues occur

#### **Free Plan Limits:**
- **200 emails/month** on free plan
- **Upgrade to paid plan** if you need more

#### **Troubleshooting:**
- **No emails received?** Check spam folder and EmailJS logs
- **Form errors?** Check browser console for error messages
- **Template issues?** Test templates in EmailJS dashboard

### **🎯 Your Configuration Summary**

After setup, you should have:
- ✅ EmailJS account created
- ✅ Email service connected (Gmail/Outlook/etc.)
- ✅ Email template created with proper formatting
- ✅ Public key, Service ID, and Template ID copied
- ✅ Website code updated with your credentials
- ✅ Form tested and working
- ✅ Email notifications received

### **📞 Support**

If you encounter issues:
1. **Check EmailJS documentation:** [https://www.emailjs.com/docs/](https://www.emailjs.com/docs/)
2. **Review browser console** for error messages
3. **Test in EmailJS dashboard** first
4. **Check email service connection** in EmailJS

Your booking form is now fully integrated with EmailJS and ready to capture leads professionally!
